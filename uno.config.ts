import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetTypography,
  presetUno,
  transformerDirectives,
  transformerVariantGroup
} from 'unocss'

export default defineConfig({
  shortcuts: [
    // 布局快捷方式
    ['flex-center', 'flex justify-center items-center'],
    ['flex-col-center', 'flex flex-col justify-center items-center'],
    ['flex-between', 'flex justify-between items-center'],
    ['flex-around', 'flex justify-around items-center'],
    ['absolute-center', 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'],
    
    // 常用容器
    ['container-fluid', 'w-full mx-auto px-4'],
    ['container-lg', 'max-w-7xl mx-auto px-4'],
    ['container-md', 'max-w-5xl mx-auto px-4'],
    ['container-sm', 'max-w-3xl mx-auto px-4'],
    
    // 按钮样式
    ['btn', 'px-4 py-2 rounded cursor-pointer border-none outline-none transition-all duration-200'],
    ['btn-primary', 'btn bg-blue-500 text-white hover:bg-blue-600'],
    ['btn-secondary', 'btn bg-gray-500 text-white hover:bg-gray-600'],
    ['btn-success', 'btn bg-green-500 text-white hover:bg-green-600'],
    
    // 卡片样式
    ['card', 'bg-white rounded-lg shadow-sm border border-gray-200'],
    ['card-hover', 'card hover:shadow-md transition-shadow duration-200'],
    
    // 文本样式
    ['text-gradient', 'bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent']
  ],
  theme: {
    colors: {
      // 广西门户主题色
      primary: {
        50: '#f0f9ff',
        100: '#e0f2fe',
        200: '#bae6fd',
        300: '#7dd3fc',
        400: '#38bdf8',
        500: '#0ea5e9',
        600: '#0284c7',
        700: '#0369a1',
        800: '#075985',
        900: '#0c4a6e'
      },
      // 南宁特色色彩
      nanning: {
        green: '#059669',
        gold: '#f59e0b',
        blue: '#3b82f6'
      }
    },
    fontFamily: {
      sans: ['PingFang SC', 'Microsoft YaHei', 'sans-serif'],
      serif: ['Georgia', 'serif'],
      mono: ['Consolas', 'Monaco', 'Courier New', 'monospace']
    },
    breakpoints: {
      xs: '480px',
      sm: '768px',
      md: '1024px',
      lg: '1280px',
      xl: '1536px'
    }
  },
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
      collections: {
        ep: () => import('@iconify-json/ep/icons.json').then(i => i.default)
      },
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle'
      }
    }),
    presetTypography()
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup()
  ]
})