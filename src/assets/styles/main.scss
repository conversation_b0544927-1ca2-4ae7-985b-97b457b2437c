// UnoCSS 由 Vite 插件自动处理，不需要手动导入
// 简化样式：合并 variables/base/mixins 到本文件，避免多余 SCSS 文件

/* ========== 主题变量（PC 项目） ========== */
:root {
  /* 主题色 */
  --color-primary: #0ea5e9;
  --color-primary-light: #38bdf8;
  --color-primary-dark: #0284c7;
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-900: #0c4a6e;

  /* 中性色 */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* 功能色 */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* 字体与间距 */
  --font-family-sans: "PingFang SC", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Consolas", "Monaco", "Courier New", monospace;
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  --spacing-xs: 0.25rem;  /* 4px */
  --spacing-sm: 0.5rem;   /* 8px */
  --spacing-md: 1rem;     /* 16px */
  --spacing-lg: 1.5rem;   /* 24px */
  --spacing-xl: 2rem;     /* 32px */
  --spacing-2xl: 3rem;    /* 48px */
  --spacing-3xl: 4rem;    /* 64px */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-full: 9999px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* 深色模式变量覆盖 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-white: #1f2937;
    --color-gray-50: #111827;
    --color-gray-100: #1f2937;
    --color-gray-200: #374151;
    --color-gray-300: #4b5563;
    --color-gray-400: #6b7280;
    --color-gray-500: #9ca3af;
    --color-gray-600: #d1d5db;
    --color-gray-700: #e5e7eb;
    --color-gray-800: #f3f4f6;
    --color-gray-900: #f9fafb;
  }
}

/* ========== 使用 UnoCSS Reset（normalize）处理基础样式，移除本地 Reset ========== */

// Element Plus 自定义主题变量
:root {
  --el-color-primary: var(--color-primary);
  --el-color-primary-light-3: var(--color-primary-light);
  --el-color-primary-light-5: var(--color-primary-100);
  --el-color-primary-light-7: var(--color-primary-50);
  --el-color-primary-light-8: var(--color-primary-50);
  --el-color-primary-light-9: var(--color-primary-50);
  --el-color-primary-dark-2: var(--color-primary-dark);
  
  --el-font-family: var(--font-family-sans);
  --el-border-radius-base: var(--border-radius-md);
  --el-border-radius-small: var(--border-radius-sm);
  --el-border-radius-round: var(--border-radius-full);
  
  --el-transition-duration: var(--transition-fast);
  --el-transition-duration-fast: var(--transition-fast);
  
  --el-box-shadow: var(--shadow-sm);
  --el-box-shadow-light: var(--shadow-sm);
  --el-box-shadow-base: var(--shadow-md);
  --el-box-shadow-dark: var(--shadow-lg);
}
body{
  margin: 0;
}
// 全局工具类
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

// 页面过渡动画
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease-in-out;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 自定义滚动条样式
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--color-gray-100);
    border-radius: var(--border-radius-full);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-gray-300);
    border-radius: var(--border-radius-full);
    
    &:hover {
      background: var(--color-gray-400);
    }
  }
}

// 加载状态（替换 mixin 为纯 CSS）
.loading {
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-primary-100);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

//（PC 项目）不提供移动端/平板端隐藏工具类

// 文本选择颜色
::selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-900);
}

::-moz-selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-900);
}
