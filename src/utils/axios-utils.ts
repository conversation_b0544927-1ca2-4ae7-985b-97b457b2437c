import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { ElMessage } from 'element-plus'

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  skipErrorHandler?: boolean
  skipLoadingHandler?: boolean
}

// 响应数据接口
export interface ResponseData<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// API 错误类
export class ApiError extends Error {
  public code: number
  public details?: any

  constructor(message: string, code: number, details?: any) {
    super(message)
    this.name = 'ApiError'
    this.code = code
    this.details = details
  }
}

// 创建 axios 实例
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: 10000,
    withCredentials: true,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  })

  return instance
}

// 主 axios 实例
const axiosInstance = createAxiosInstance()

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    // 添加认证 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳
    config.headers['X-Request-Time'] = Date.now().toString()

    // 添加城市信息
    const currentCity = localStorage.getItem('selectedCity')
    if (currentCity) {
      config.headers['X-City-Code'] = currentCity
    }

    console.log('Request:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    
    // 统一的响应数据结构处理
    if (data && typeof data === 'object' && 'code' in data) {
      if (data.code === 200 || data.code === 0) {
        return response
      } else {
        // 业务错误
        const error = new ApiError(data.message || '业务处理失败', data.code, data)
        return Promise.reject(error)
      }
    }

    // 直接返回数据（非标准格式）
    return response
  },
  async (error: AxiosError) => {
    const { config, response } = error

    // 网络错误处理
    if (!response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(new ApiError('网络连接失败', 0))
    }

    const { status, data } = response
    const requestConfig = config as RequestConfig

    // HTTP 状态码错误处理
    switch (status) {
      case 401:
        // 未授权，清除本地认证信息
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        
        if (!requestConfig?.skipErrorHandler) {
          ElMessage.error('登录已过期，请重新登录')
          // 可以在这里跳转到登录页
          window.location.href = '/login'
        }
        break

      case 403:
        if (!requestConfig?.skipErrorHandler) {
          ElMessage.error('权限不足，无法访问该资源')
        }
        break

      case 404:
        if (!requestConfig?.skipErrorHandler) {
          ElMessage.error('请求的资源不存在')
        }
        break

      case 429:
        if (!requestConfig?.skipErrorHandler) {
          ElMessage.error('请求过于频繁，请稍后再试')
        }
        break

      case 500:
        if (!requestConfig?.skipErrorHandler) {
          ElMessage.error('服务器内部错误')
        }
        break

      case 502:
      case 503:
      case 504:
        if (!requestConfig?.skipErrorHandler) {
          ElMessage.error('服务器暂时不可用，请稍后再试')
        }
        break

      default:
        if (!requestConfig?.skipErrorHandler) {
          const message = (data as any)?.message || `请求失败 (${status})`
          ElMessage.error(message)
        }
        break
    }

    const apiError = new ApiError(
      (data as any)?.message || error.message || '请求失败',
      status,
      data
    )

    return Promise.reject(apiError)
  }
)

// Token 刷新机制
let isRefreshing = false
let failedQueue: Array<{
  resolve: (value?: any) => void
  reject: (reason?: any) => void
}> = []

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })
  
  failedQueue = []
}

// 刷新 token 的函数
const refreshAuthToken = async (): Promise<string | null> => {
  try {
    const refreshToken = localStorage.getItem('refreshToken')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await axios.post('/auth/refresh', {
      refreshToken
    })

    const { data } = response.data
    const newToken = data.token
    const newRefreshToken = data.refreshToken

    localStorage.setItem('token', newToken)
    localStorage.setItem('refreshToken', newRefreshToken)

    return newToken
  } catch (error) {
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    throw error
  }
}

// 为 401 错误添加 token 刷新逻辑
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`
          return axiosInstance(originalRequest)
        }).catch(err => {
          return Promise.reject(err)
        })
      }

      originalRequest._retry = true
      isRefreshing = true

      try {
        const newToken = await refreshAuthToken()
        processQueue(null, newToken)
        originalRequest.headers.Authorization = `Bearer ${newToken}`
        return axiosInstance(originalRequest)
      } catch (refreshError) {
        processQueue(refreshError, null)
        return Promise.reject(refreshError)
      } finally {
        isRefreshing = false
      }
    }

    return Promise.reject(error)
  }
)

// 通用请求方法
export const request = <T = any>(config: RequestConfig): Promise<ResponseData<T>> => {
  return axiosInstance(config).then(response => response.data)
}

// HTTP 方法封装
export const http = {
  get: <T = any>(url: string, config?: RequestConfig) => 
    request<T>({ ...config, method: 'GET', url }),

  post: <T = any>(url: string, data?: any, config?: RequestConfig) => 
    request<T>({ ...config, method: 'POST', url, data }),

  put: <T = any>(url: string, data?: any, config?: RequestConfig) => 
    request<T>({ ...config, method: 'PUT', url, data }),

  patch: <T = any>(url: string, data?: any, config?: RequestConfig) => 
    request<T>({ ...config, method: 'PATCH', url, data }),

  delete: <T = any>(url: string, config?: RequestConfig) => 
    request<T>({ ...config, method: 'DELETE', url })
}

// 文件上传
export const uploadFile = (
  url: string, 
  file: File, 
  onProgress?: (percent: number) => void
): Promise<ResponseData> => {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    method: 'POST',
    url,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (progressEvent.total && onProgress) {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percent)
      }
    }
  })
}

// 下载文件
export const downloadFile = async (url: string, filename?: string): Promise<void> => {
  try {
    const response = await axiosInstance({
      method: 'GET',
      url,
      responseType: 'blob'
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('Download failed:', error)
    ElMessage.error('文件下载失败')
  }
}

// 取消请求
export const createCancelToken = () => {
  const source = axios.CancelToken.source()
  return {
    token: source.token,
    cancel: source.cancel
  }
}

// 导出实例供特殊情况使用
export { axiosInstance as axios }
export default http