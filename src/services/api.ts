import http from '@/utils/axios-utils'
import type { NewsArticle, UserInfo, LoginForm, PaginationData } from '@/types'

// 新闻相关 API
export const newsApi = {
  // 获取新闻列表
  getNewsList: (params: {
    page?: number
    pageSize?: number
    category?: string
    tag?: string
    keyword?: string
  }) => {
    return http.get<PaginationData<NewsArticle>>('/news', { params })
  },

  // 获取新闻详情
  getNewsDetail: (id: string) => {
    return http.get<NewsArticle>(`/news/${id}`)
  },

  // 获取热门新闻
  getHotNews: (limit?: number) => {
    return http.get<NewsArticle[]>('/news/hot', { params: { limit } })
  },

  // 获取新闻分类
  getNewsCategories: () => {
    return http.get<Array<{ name: string; value: string; count: number }>>('/news/categories')
  }
}

// 用户相关 API
export const userApi = {
  // 用户登录
  login: (data: LoginForm) => {
    return http.post<{
      token: string
      refreshToken: string
      userInfo: UserInfo
    }>('/auth/login', data)
  },

  // 用户登出
  logout: () => {
    return http.post('/auth/logout')
  },

  // 刷新 Token
  refreshToken: (refreshToken: string) => {
    return http.post<{
      token: string
      refreshToken: string
    }>('/auth/refresh', { refreshToken })
  },

  // 获取用户信息
  getUserInfo: () => {
    return http.get<UserInfo>('/user/profile')
  },

  // 更新用户信息
  updateProfile: (data: Partial<UserInfo>) => {
    return http.put<UserInfo>('/user/profile', data)
  },

  // 修改密码
  changePassword: (data: {
    oldPassword: string
    newPassword: string
  }) => {
    return http.put('/user/password', data)
  }
}

// 城市服务相关 API
export const cityApi = {
  // 获取城市配置
  getCityConfig: (cityCode: string) => {
    return http.get(`/cities/${cityCode}/config`)
  },

  // 获取城市服务列表
  getCityServices: (cityCode: string) => {
    return http.get(`/cities/${cityCode}/services`)
  },

  // 获取城市统计数据
  getCityStatistics: (cityCode: string) => {
    return http.get(`/cities/${cityCode}/statistics`)
  }
}

// 搜索相关 API
export const searchApi = {
  // 全文搜索
  search: (params: {
    keyword: string
    page?: number
    pageSize?: number
    type?: string
    cityCode?: string
  }) => {
    return http.get('/search', { params })
  },

  // 获取搜索建议
  getSuggestions: (keyword: string) => {
    return http.get<string[]>('/search/suggestions', { params: { keyword } })
  },

  // 获取热门搜索
  getHotSearches: (cityCode?: string) => {
    return http.get<string[]>('/search/hot', { params: { cityCode } })
  }
}

// 文件上传相关 API
export const uploadApi = {
  // 上传图片
  uploadImage: (file: File, onProgress?: (percent: number) => void) => {
    const formData = new FormData()
    formData.append('image', file)
    return http.post<{ url: string }>('/upload/image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(percent)
        }
      }
    })
  },

  // 上传文件
  uploadFile: (file: File, onProgress?: (percent: number) => void) => {
    const formData = new FormData()
    formData.append('file', file)
    return http.post<{ url: string; filename: string }>('/upload/file', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(percent)
        }
      }
    })
  }
}

// 系统配置 API
export const systemApi = {
  // 获取系统配置
  getSystemConfig: () => {
    return http.get('/system/config')
  },

  // 获取菜单配置
  getMenuConfig: (cityCode: string) => {
    return http.get(`/system/menus/${cityCode}`)
  },

  // 获取页面配置
  getPageConfig: (pageKey: string) => {
    return http.get(`/system/pages/${pageKey}`)
  }
}

export default {
  news: newsApi,
  user: userApi,
  city: cityApi,
  search: searchApi,
  upload: uploadApi,
  system: systemApi
}