<template>
  <header class="header bg-white">
    <!-- 顶部区域 -->
    <div class="top-header bg-white border-b border-gray-200">
      <div class="container-lg py-2">
        <div class="flex items-center justify-between text-sm text-gray-600">
          <div class="flex items-center space-x-4">
            <CitySelector class="hidden md:block" />
            <span>您好，欢迎来到广西壮族自治区“数智人社”—广西就业平台！</span>
          </div>
          <div class="flex items-center space-x-4">
            <div
              class="flex items-center space-x-2 bg-#E4E7F1 rounded-20px px-15px py-7px cursor-pointer"
            >
              <img
                src="//image.gxrc.com/thirdParty/gxjy/pc/home/<USER>"
                class="w-21px h-21px"
                alt=""
              />
              <span>AI智能客服</span>
            </div>
            <!-- 登录按钮组 -->
            <div class="flex items-center space-x-2">
              <div class="login-btn enterprise">企业登录</div>
              <div class="login-btn personal">个人登录</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主头部区域 -->
    <div class="main-header py-4">
      <div class="container-lg">
        <div class="flex items-center justify-between">
          <!-- Logo 和标题 -->
          <div class="flex items-center space-x-4">
            <router-link
              :to="cityStore.getCityHomePath"
              class="flex items-center space-x-3 hover:opacity-80 transition-opacity"
            >
              <el-image
                src="//image.gxrc.com/thirdParty/gxjy/pc/home/<USER>"
                class="w-265px h-72px"
              />
            </router-link>
          </div>

          <!-- 中间搜索区域 -->
          <div class="flex-1 max-w-2xl mx-8">
            <div class="search-container">
              <div class="search-wrapper">
                <!-- 左侧选择器 -->
                <div class="search-selector">
                  <el-dropdown @command="handleTypeChange" trigger="click">
                    <div class="selector-content">
                      <span>{{ searchType === 1 ? "找工作" : "找公司" }}</span>
                      <i class="i-ep-arrow-down text-xs ml-1"></i>
                    </div>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="1">找工作</el-dropdown-item>
                        <el-dropdown-item command="2">找公司</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
                <!-- 中间输入框 -->
                <div class="search-input-wrapper">
                  <input
                    v-model="searchKeyword"
                    type="text"
                    placeholder="输入您想要查询的关键词"
                    class="search-input"
                    @keyup.enter="handleSearch"
                  />
                </div>
                <!-- 右侧搜索按钮 -->
                <button class="search-btn" @click="handleSearch">搜索</button>
              </div>
            </div>
            <!-- 热门搜索 -->
            <div class="hot-search mt-12px ml-30px text-sm text-#666">
              热门搜索：
              <span
                v-for="tag in hotSearchTags"
                :key="tag"
                class="hot-tag"
                @click="
                  searchKeyword = tag;
                  handleSearch();
                "
              >
                {{ tag }}
              </span>
            </div>
          </div>

          <!-- 右侧功能区 -->
          <div class="flex items-center space-x-4">
            <!-- 二维码 -->
            <div class="qr-code-section">
              <div class="text-center">
                <el-image
                  src="//image.gxrc.com/thirdParty/gxjy/pc/home/<USER>"
                  class="w-92px h-95px"
                />
              </div>
              <div class="qr-text text-xs text-center text-#2C3137">
                <div>扫码查看“广西就业”小程序</div>
                
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导航菜单栏 -->
    <div class="nav-menu">
      <div class="container-lg">
        <nav class="flex items-center justify-between">
          <div class="flex items-center space-x-0">
            <router-link
              v-for="menu in navigationMenus"
              :key="menu.path"
              :to="cityStore.getCityPagePath(menu.path)"
              class="nav-item"
              :class="{ 'nav-item-active': isMenuActive(menu.path) }"
            >
              {{ menu.name }}
            </router-link>
          </div>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useCityStore } from "@/stores/cityStore";

import CitySelector from "@/components/common/CitySelector.vue";
import { ElMessage } from "element-plus";

// Store
const cityStore = useCityStore();

const router = useRouter();

// 组件状态
const showMobileMenu = ref(false);
const searchKeyword = ref("");
const searchType = ref(1);

// 热门搜索标签
const hotSearchTags = ref([
  "会计",
  "销售",
  "人事",
  "司机",
  "文员",
  "JAVA工程师",
  "主治医师",
  "设计师",
  "物流",
  "中介",
  "操作员",
  "教师",
]);

// 导航菜单配置
const navigationMenus = computed(() => [
  {
    name: "首页",
    path: "",
  },
  {
    name: "用工需求地图",
    path: "/demand-survey",
  },
  {
    name: "招聘专题",
    path: "/recruitment",
  },
  {
    name: "区外招聘",
    path: "/external-recruitment",
  },
  {
    name: "粤桂劳务协作",
    path: "/labor-cooperation",
  },
  {
    name: "零工市场",
    path: "/gig-market",
  },
  {
    name: "职业指导",
    path: "/training-guidance",
  },
  {
    name: "创业服务",
    path: "/entrepreneurship",
  },
  {
    name: "就业见习",
    path: "/employment-office",
  },
  {
    name: "服务机构",
    path: "/service-agencies",
  },
]);

// 搜索处理
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning("请输入搜索关键词");
    return;
  }
  console.log(
    "搜索关键词:",
    searchKeyword.value,
    "搜索类型:",
    searchType.value
  );
  // TODO: 实现搜索逻辑
  ElMessage.success(`搜索关键词: ${searchKeyword.value}`);
};

// 处理搜索类型变化
const handleTypeChange = (type: string) => {
  searchType.value = parseInt(type);
};

// 判断菜单是否激活
const isMenuActive = (path: string) => {
  const currentPath = router.currentRoute.value.path;
  return cityStore.isCurrentPath(path)(currentPath);
};

// 监听路由变化，关闭移动端菜单
watch(
  () => router.currentRoute.value.path,
  () => {
    showMobileMenu.value = false;
  }
);

// 点击外部关闭移动端菜单
let clickOutsideHandler: ((event: Event) => void) | null = null;

onMounted(() => {
  clickOutsideHandler = (event: Event) => {
    const target = event.target as Element;
    const header = document.querySelector(".header");
    if (header && !header.contains(target) && showMobileMenu.value) {
      showMobileMenu.value = false;
    }
  };

  // 使用 passive 监听器提升性能
  document.addEventListener("click", clickOutsideHandler, { passive: true });
});

onUnmounted(() => {
  if (clickOutsideHandler) {
    document.removeEventListener("click", clickOutsideHandler);
    clickOutsideHandler = null;
  }
});
</script>

<style scoped lang="scss">
.header {
  background-color: rgba(255, 255, 255, 0.98);
  // 移除 backdrop-filter 以提升性能，避免抖动
  // backdrop-filter: blur(8px);
}

// 导航菜单样式
.nav-menu {
  background: linear-gradient(180deg, #006bd5 0%, #0c86ff 100%);
}

.nav-item {
  display: inline-block;
  padding: 12px 24px;
  color: white;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    background: linear-gradient(180deg, #00ebc3 0%, #00b0ff 100%);
    color: white;
  }
}

.nav-item-active {
  background: linear-gradient(180deg, #00ebc3 0%, #00b0ff 100%);
  color: white;
}

.nav-link {
  @apply px-3 py-2 text-gray-700 hover:text-primary-500 hover:bg-gray-100 rounded-lg transition-all font-medium;
  display: flex;
  align-items: center;
}

.nav-link-active {
  @apply text-primary-500 bg-primary-50;
}

.login-btn {
  @apply cursor-pointer rounded-20px text-white text-14px px-23px py-7px;
}
.enterprise {
  background: linear-gradient(301deg, #0b83fb 0%, #0a23ff 100%);
}
.personal {
  background: linear-gradient(301deg, #00ebc3 0%, #00b0ff 100%);
}

// 搜索框样式
.search-container {
  background: #007bf6;
  border-radius: 26px;
  padding: 2px;

  .search-wrapper {
    display: flex;
    align-items: center;
    background: transparent;
    border-radius: 24px;
    overflow: hidden;
    height: 46px;

    .search-selector {
      line-height: 46px;
      height: 46px;
      flex-shrink: 0;
      padding: 0 20px;
      border-right: 1px solid #e5e7eb;
      cursor: pointer;
      background: white;
      border-radius: 24px 0 0 24px;

      .selector-content {
        display: flex;
        align-items: center;
        color: #333;
        font-size: 14px;
        white-space: nowrap;

        &:hover {
          color: #007bf6;
        }
      }
    }

    .search-input-wrapper {
      flex: 1;
      height: 100%;
      background: white;

      .search-input {
        width: 100%;
        height: 100%;
        border: none;
        outline: none;
        padding: 0 20px;
        font-size: 14px;
        background: transparent;
        box-sizing: border-box;

        &::placeholder {
          color: #999;
        }

        &:focus {
          outline: none;
        }
      }
    }

    .search-btn {
      flex-shrink: 0;
      background-color: #007bf6;
      color: white;
      border: none;
      padding: 0 43px;
      height: 100%;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      border-radius: 0 24px 24px 0;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #0056cc;
      }

      &:active {
        background-color: #004bb3;
      }
    }
  }
}

// Element Plus Dropdown 样式覆盖
:deep(.el-dropdown) {
  width: 100%;
  height: 100%;

  .el-dropdown__caret-button {
    display: none;
  }
}

// 热门搜索样式
.hot-search {
  .hot-tag {
    color: #666666;
    cursor: pointer;
    margin-left: 4px;
    margin-right: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s ease;

    &:hover {
      color: #0056cc;
    }

    // &:not(:last-child)::after {
    //   content: '';
    //   display: inline-block;
    //   width: 1px;
    //   height: 12px;
    //   background-color: #e5e7eb;
    //   margin-left: 8px;
    //   vertical-align: middle;
    // }
  }
}
</style>
