<template>
  <div class="app-layout min-h-screen flex flex-col bg-gray-50">
    <!-- Header -->
    <HeaderComponent 
      v-if="showHeader"
      class="header-sticky"
    />

    <!-- Main Content -->
    <main class="flex-1 relative">
      <router-view v-slot="{ Component, route }">
        <Transition name="page" mode="out-in">
          <component 
            :is="Component" 
            :key="route.path"
            class="min-h-full"
          />
        </Transition>
      </router-view>
    </main>

    <!-- Footer -->
    <CityFooter 
      v-if="showFooter"
    />

    <!-- Global Loading -->
    <Transition name="fade">
      <div 
        v-if="isLoading"
        id="app-loading" 
        class="fixed inset-0 bg-white flex-center z-50"
      >
        <div class="text-center">
          <div class="loading mb-4"></div>
          <p class="text-gray-600">加载中...</p>
        </div>
      </div>
    </Transition>

    <!-- Back to Top -->
    <Transition name="fade">
      <button
        v-show="showBackToTop"
        @click="scrollToTop"
        class="fixed bottom-6 right-6 w-12 h-12 bg-primary-500 text-white rounded-full flex-center shadow-lg hover:bg-primary-600 transition-all z-40"
        title="返回顶部"
      >
        <i class="i-ep-arrow-up text-xl"></i>
      </button>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import HeaderComponent from '@/components/layout/HeaderComponent.vue'
import CityFooter from '@/components/layout/CityFooter.vue'

// 组件状态
const showBackToTop = ref(false)
const isLoading = ref(true)

// 路由信息
const route = useRoute()
const showHeader = computed(() => route.meta.showHeader !== false)
const showFooter = computed(() => route.meta.showFooter !== false)

// 滚动处理
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 生命周期
onMounted(() => {
  window.addEventListener('scroll', handleScroll, { passive: true })
  
  // 隐藏初始加载状态
  nextTick(() => {
    isLoading.value = false
  })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

// 页面标题更新
watch(
  () => route.meta.title,
  (newTitle) => {
    if (newTitle) {
      document.title = `${newTitle} - 广西门户网站`
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">

.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header-sticky {
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
}

// 页面过渡动画
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease-in-out;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// Loading spinner 居中样式
.loading {
  margin: 0 auto;
}


</style>
