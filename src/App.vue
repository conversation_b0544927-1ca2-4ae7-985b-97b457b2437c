<template>
  <div id="app" class="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useCityStore } from '@/stores/cityStore'


// 初始化 Store
const cityStore = useCityStore()


// 应用初始化
onMounted(() => {
  // 初始化城市 Store
  cityStore.initialize()
  

  
  // 监听在线状态
  const handleOnline = () => {
    console.log('网络连接已恢复')
  }
  
  const handleOffline = () => {
    console.log('网络连接已断开')
  }
  
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  onUnmounted(() => {
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
  })
})

// 页面标题已在路由守卫中统一处理，此处不再重复设置
</script>

<style lang="scss">
// 使用新的 @use 模块系统

.app {
  min-height: 100vh;
  font-family: var(--font-family-sans);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 全局滚动条样式
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-300) var(--color-gray-100);
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--border-radius-full);
}

*::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--border-radius-full);
  
  &:hover {
    background: var(--color-gray-400);
  }
}



// 全局文本选择样式
::selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-900);
}

::-moz-selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-900);
}

// 图片懒加载占位
img[loading="lazy"] {
  background: linear-gradient(90deg, #f0f0f0 25%, transparent 37%, #f0f0f0 63%);
  background-size: 400% 100%;
  animation: loading-placeholder 1.5s ease-in-out infinite;
}

@keyframes loading-placeholder {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: -100% 50%;
  }
}

// 无障碍辅助类
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

//（PC 项目）移除移动端/平板端相关的隐藏工具类

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    color: black !important;
    background: white !important;
  }
  
  a {
    text-decoration: underline !important;
  }
  
  .btn {
    border: 1px solid black !important;
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .app {
    color: var(--color-gray-100);
    background-color: var(--color-gray-900);
  }
}

// 减少动画 (用户偏好设置)
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
  
  .btn {
    border-width: 2px !important;
  }
}
</style>
